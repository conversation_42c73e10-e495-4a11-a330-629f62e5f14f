package ktwf

import (
	"context"
	"fmt"
	"github.com/davecgh/go-spew/spew"
	"time"

	"gitlab.rp.konvery.work/platform/pkg/kutil"
	"gitlab.rp.konvery.work/platform/pkg/log"
	"gitlab.rp.konvery.work/platform/pkg/wf"
	"gitlab.rp.konvery.work/platform/pkg/wf/tracing"
	wutil "gitlab.rp.konvery.work/platform/pkg/wf/wfutil"
	"go.temporal.io/sdk/client"
)

var defCfg *Config

func Init(cfg *Config) {
	fmt.Println("---> Init Workflow && Activity.")
	defCfg = cfg

	if !dbgmode {
		// check mandatory args
		if defCfg.Master.Image == "" {
			image, err := GetContainerImage()
			kutil.Assert(err)
			defCfg.Master.Image = image
			log.NewHelper(cfg.Logger).Info(context.Background(), "inferred master image", "name", image)
		}
		if cfg.Master.Image == "" {
			panic(fmt.Errorf("image is not set"))
		}
	}

	wf.Init(&cfg.Config)
	wf.RegisterWorkflow(WFSchedulerWorkflow, WFMainWorkflow)
	wf.RegisterActivity(NewActivities(cfg.Logger))
}

// StartWorkflow starts a workflow asynchronously.
// If shareWorker is false, it will start a new worker to execute the workflow.
func StartWorkflow(ctx context.Context, name string, spec *WorkflowSpec, shareWorker bool) (
	r client.WorkflowRun, err error) {
	fmt.Println("---> starting workflow: ", name)
	if err = spec.Validate(); err != nil {
		return r, fmt.Errorf("failed to start workflow %v: %w", name, err)
	}

	tmo := spec.Workflow.Timeout
	if tmo == 0 {
		tmo = 72 * time.Hour
	}
	var wffn any = WFSchedulerWorkflow
	surffix := "-scheduler"
	if shareWorker || dbgmode {
		surffix = ""
		wffn = WFMainWorkflow
	}
	fmt.Println("---> bgmode：", shareWorker, dbgmode, surffix)
	wfc := &WorkflowContext{Name: name, Spec: spec}
	ctx = tracing.PropagateTraceID(ctx)
	options := client.StartWorkflowOptions{
		ID:                       wfc.Name + surffix,
		TaskQueue:                defCfg.Taskq,
		RetryPolicy:              wutil.RetryPolicy(10),
		WorkflowExecutionTimeout: tmo,
	}
	spew.Dump("---> workflow options: ", options, wffn, wfc)
	r, err = wf.DefaultClient().ExecuteWorkflow(ctx, options, wffn, wfc)
	if err != nil {
		return r, fmt.Errorf("failed to start workflow %v: %w", name, err)
	}
	return
}

// ExecWorkflow executes a workflow synchronously.
func ExecWorkflow(ctx context.Context, name string, spec *WorkflowSpec, shareWorker bool) (res string, err error) {
	r, err := StartWorkflow(ctx, name, spec, shareWorker)
	if err != nil {
		return
	}
	err = r.Get(ctx, &res)
	if err != nil {
		return "", fmt.Errorf("failed to get result of workflow %v: %w", name, err)
	}
	return
}
