package wf

import (
	"context"
	"fmt"
	"os"
	"reflect"
	"runtime"
	"strings"

	"gitlab.rp.konvery.work/platform/pkg/log"
	"gitlab.rp.konvery.work/platform/pkg/wf/tracing"
	wutil "gitlab.rp.konvery.work/platform/pkg/wf/wfutil"

	"go.temporal.io/sdk/client"
	"go.temporal.io/sdk/converter"
	"go.temporal.io/sdk/worker"
)

var (
	defCfg    *Config
	defcli    client.Client
	defworker worker.Worker

	EnvTemporalTaskq = "temporal.task_queue"
)

type Config struct {
	Addr      string
	Taskq     string
	Namespace string
	Logger    log.Logger
}

func Init(cfg *Config) {
	defCfg = cfg

	var err error
	defcli, err = client.Dial(client.Options{
		HostPort:           cfg.Addr,
		Namespace:          cfg.Namespace,
		Logger:             log.NewTemporalLogger(cfg.Logger),
		DataConverter:      GetDataConverter(),
		ContextPropagators: tracing.GetContextPropagators(),
	})
	if err != nil {
		panic(fmt.Errorf("failed to create client: %w", err))
	}

	if q := os.Getenv(EnvTemporalTaskq); q != "" && q != cfg.Taskq {
		cfg.Taskq = q
		log.NewHelper(cfg.Logger).Info(context.Background(), "temporal task queue overridden", "taskq", q)
	}

	defworker = worker.New(defcli, cfg.Taskq, worker.Options{}) // EnableSessionWorker: true
}

func GetDataConverter() converter.DataConverter {
	return converter.NewCompositeDataConverter(
		converter.NewNilPayloadConverter(),
		converter.NewByteSlicePayloadConverter(),

		// Order is important here. Both ProtoJsonPayload and ProtoPayload converters check for the same proto.Message
		// interface. The first match (ProtoJsonPayload in this case) will always be used for serialization.
		// Deserialization is controlled by metadata, therefore both converters can deserialize corresponding data format
		// (JSON or binary proto).
		converter.NewProtoJSONPayloadConverter(),
		converter.NewProtoPayloadConverter(),

		NewJSONPayloadConverter(),

		converter.NewJSONPayloadConverter(),
	)
}

func DefaultClient() client.Client { return defcli }

// RegisterWorkflow registers workflow functions with the worker.
func RegisterWorkflow(a ...any) {
	fmt.Println("Registering workflow...")
	for _, v := range a {
		fmt.Println("---> Register workflow: ", pickWfName(v, false))
		defworker.RegisterWorkflow(v)
	}
	fmt.Println("-------------------------")
}

func pickWfName(v interface{}, activity bool) string {
	//fnType := reflect.TypeOf(v)
	//if fnType.Kind() != reflect.Func {
	//	return fmt.Errorf("expected a func as input but was %s", fnType.Kind()).Error()
	//}
	//spew.Dump(v)
	var fnName string
	if activity {
		if v == nil {
			return "<nil>"
		}

		t := reflect.TypeOf(v)
		//spew.Dump(t)

		// 如果是指针，取 Elem
		if t.Kind() == reflect.Ptr {
			t = t.Elem()
		}

		// 如果是结构体实例，返回结构体类型名
		if t.Kind() == reflect.Struct {
			return t.Name()
		}

		// 如果是函数，返回函数类型
		if t.Kind() == reflect.Func {
			return t.Name() // Go 函数可能没有名字，返回空字符串
		}

		// 默认返回类型字符串
		return t.String()
	} else {
		fnName, _ = getFunctionName(v)
	}
	return fnName
}

func listMethods(a any) []string {
	t := reflect.TypeOf(a)
	var methods []string

	// 获取指针接收者方法
	for i := 0; i < t.NumMethod(); i++ {
		methods = append(methods, t.Method(i).Name)
	}

	// 如果是指针，获取值类型方法
	if t.Kind() == reflect.Ptr {
		tElem := t.Elem()
		for i := 0; i < tElem.NumMethod(); i++ {
			methods = append(methods, tElem.Method(i).Name)
		}
	}

	return methods
}

func getFunctionName(i interface{}) (name string, isMethod bool) {
	if fullName, ok := i.(string); ok {
		//fmt.Println(111, fullName)
		return fullName, false
	}
	fullName := runtime.FuncForPC(reflect.ValueOf(i).Pointer()).Name()
	//fmt.Println(222, fullName)
	// Full function name that has a struct pointer receiver has the following format
	// <prefix>.(*<type>).<function>
	isMethod = strings.ContainsAny(fullName, "*")
	elements := strings.Split(fullName, ".")
	shortName := elements[len(elements)-1]
	// This allows to call activities by method pointer
	// Compiler adds -fm suffix to a function name which has a receiver
	// Note that this works even if struct pointer used to get the function is nil
	// It is possible because nil receivers are allowed.
	// For example:
	// var a *Activities
	// ExecuteActivity(ctx, a.Foo)
	// will call this function which is going to return "Foo"
	return strings.TrimSuffix(shortName, "-fm"), isMethod
}

// RegisterActivity registers activity functions or pointers to structures with the worker.
func RegisterActivity(a ...any) {
	fmt.Println("Registering activity...")
	for _, v := range a {
		fmt.Println("---> Register activity: ", pickWfName(v, true), " Methods: ", listMethods(v))
		defworker.RegisterActivity(v)
	}
	fmt.Println("-------------------------")
}

// Run the worker in a blocking fashion.
// Stop worker with Ctrl+C.
func Run() {
	err := defworker.Run(worker.InterruptCh())
	if err != nil {
		panic(fmt.Errorf("worker exited unexpectedly: %w", err))
	}
}

// Start the worker in a non-blocking fashion.
// Call Stop() to stop the worker.
func Start() {
	if err := defworker.Start(); err != nil {
		panic(err)
	}
}

// Stop the worker.
func Stop() {
	defworker.Stop()
}

// CancelWorkflow requests cancellation of a workflow in execution.
func CancelWorkflow(ctx context.Context, wfid, runid string, notFoundAsOK bool) error {
	err := defcli.CancelWorkflow(ctx, wfid, runid)
	if err != nil {
		if notFoundAsOK && wutil.IsErrNotFound(err) {
			err = nil
		} else {
			err = fmt.Errorf("failed to cancel workflow %v: %w", wfid, err)
		}
	}
	return err
}

type WfArgsOpts struct {
	Workflow     any
	WorkflowArgs []any
	Options      StartWorkflowOptions
}
type StartWorkflowOptions = client.StartWorkflowOptions

// StartWorkflow starts a workflow asynchronously.
func StartWorkflow(ctx context.Context, wfid string, wf *WfArgsOpts) (client.WorkflowRun, error) {
	opts := wf.Options
	if opts.TaskQueue == "" {
		opts.TaskQueue = defCfg.Taskq
	}
	opts.ID = wfid
	ctx = tracing.PropagateTraceID(ctx)
	run, err := defcli.ExecuteWorkflow(ctx, opts, wf.Workflow, wf.WorkflowArgs...)
	if err != nil {
		return nil, fmt.Errorf("failed to start workflow %v: %w", wfid, err)
	}
	return run, err
}

// ExecWorkflow executes a workflow synchronously.
func ExecWorkflow(ctx context.Context, wfid string, wf *WfArgsOpts) error {
	r, err := StartWorkflow(ctx, wfid, wf)
	if err != nil {
		return err
	}
	err = r.Get(ctx, nil)
	if err != nil {
		return fmt.Errorf("failed to exec workflow %v: %w", wfid, err)
	}
	return nil
}

// ExecWorkflowGetResult executes a workflow synchronously and get its result.
func ExecWorkflowGetResult[T any](ctx context.Context, wfid string, wf *WfArgsOpts) (res T, err error) {
	r, err := StartWorkflow(ctx, wfid, wf)
	if err != nil {
		return
	}
	err = r.Get(ctx, &res)
	if err != nil {
		return res, fmt.Errorf("failed to get result of workflow %v: %w", wfid, err)
	}
	return
}
